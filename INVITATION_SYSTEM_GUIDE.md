# QRAnalytica Invitation System

## Overview

The invitation system allows users to share their QR code dashboards with other users. When a user shares their dashboard, the recipient receives an invitation that they can accept or reject directly from their dashboard.

## How It Works

### 1. Sharing a Dashboard

1. **Navigate to Sharing Page**: Go to `/dashboard/sharing`
2. **Send Invitation**: Enter the recipient's email address and select permission level
3. **Invitation Created**: System creates a pending invitation in the database

### 2. Receiving Invitations

1. **Login**: When the recipient logs in, they see invitation notifications at the top of their dashboard
2. **View Details**: The notification shows who sent the invitation, permission level, and when it was sent
3. **Respond**: Recipient can click "Accept" or "Reject" buttons

### 3. Invitation Response

- **Accept**: Creates a dashboard share relationship and marks invitation as accepted
- **Reject**: Marks invitation as expired (rejected)
- **Auto-cleanup**: Expired invitations are automatically filtered out

## API Endpoints

### Get User's Pending Invitations
```
GET /api/sharing/my-invitations
```
Returns all pending invitations for the current user's email address.

### Respond to Invitation
```
POST /api/sharing/respond-invitation
Body: {
  "invitation_id": "uuid",
  "action": "accept" | "reject"
}
```
Accepts or rejects a specific invitation.

## Components

### InvitationNotifications.tsx
- Displays pending invitations as notification cards
- Handles accept/reject actions
- Shows invitation details (sender, permission level, date)
- Automatically refreshes after actions

### Integration
- Added to `DashboardLayout.astro` so it appears on all dashboard pages
- Uses existing toast system for user feedback
- Responsive design with proper styling

## Database Schema

### share_invitations table
- `id`: Unique invitation ID
- `owner_id`: User who sent the invitation
- `email`: Recipient's email address
- `permission_level`: VIEW, EDIT, or DELETE
- `token`: Unique token for URL-based acceptance (backup method)
- `status`: pending, accepted, or expired
- `expires_at`: Expiration timestamp (7 days from creation)
- `created_at`: Creation timestamp

### dashboard_shares table
- `id`: Unique share ID
- `owner_id`: Dashboard owner
- `shared_with_id`: User who has access
- `permission_level`: Access level granted
- `status`: active or inactive

## Testing the System

### Step 1: Create Test Invitation
1. Login as user A (e.g., <EMAIL>)
2. Go to `/dashboard/sharing`
3. Share dashboard with user B (e.g., <EMAIL>)
4. Select permission level (VIEW, EDIT, or DELETE)
5. Click "Send Invitation"

### Step 2: View Invitation
1. Login as user B (<EMAIL>)
2. Navigate to any dashboard page (e.g., `/dashboard`)
3. You should see the invitation notification at the top
4. The notification shows:
   - Sender's name and email
   - Permission level granted
   - Date invitation was sent
   - Accept/Reject buttons

### Step 3: Respond to Invitation
1. Click "Accept" to accept the invitation
2. Or click "Reject" to decline
3. System provides feedback via toast notification
4. Invitation disappears from the list
5. If accepted, user B now has access to user A's dashboard

### Step 4: Verify Access
1. After accepting, user B can access shared dashboard features
2. Permission level determines what actions are allowed
3. Shared dashboards appear in the "Shared with Me" section

## Test Page

Visit `/test-invitations` to see a dedicated test page that shows:
- Current pending invitations for the logged-in user
- Detailed invitation information
- Direct accept/reject buttons
- Testing instructions

## Features

### Security
- Invitations are tied to specific email addresses
- Users can only respond to invitations sent to their email
- Expired invitations are automatically filtered out
- Proper authentication required for all actions

### User Experience
- Clean, intuitive notification design
- Real-time updates after actions
- Proper error handling and feedback
- Responsive design for all devices

### Permission Levels
- **VIEW**: Can view dashboard and analytics
- **EDIT**: Can view and modify QR codes
- **DELETE**: Full access including deletion rights

## Error Handling

The system handles various error scenarios:
- Invalid invitation IDs
- Expired invitations
- Already accepted invitations
- Authentication failures
- Database errors

All errors are properly communicated to users via toast notifications.

## Future Enhancements

Potential improvements:
- Email notifications when invitations are sent
- Bulk invitation management
- Invitation history and audit trail
- Custom permission levels
- Time-limited access grants
