-- Add trial and subscription tracking to users table
-- Migration: 0005_add_trial_tracking.sql

-- Add new columns to users table for trial and subscription tracking
ALTER TABLE users ADD COLUMN trial_start_date DATETIME;
ALTER TABLE users ADD COLUMN trial_end_date DATETIME;
ALTER TABLE users ADD COLUMN subscription_plan TEXT DEFAULT 'free'; -- free, professional, enterprise
ALTER TABLE users ADD COLUMN subscription_status TEXT DEFAULT 'active'; -- active, expired, cancelled
ALTER TABLE users ADD COLUMN subscription_payment_date DATETIME; -- When they paid for professional
ALTER TABLE users ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP;

-- Create trigger to handle updated_at timestamp for users
CREATE TRIGGER IF NOT EXISTS update_users_updated_at 
    AFTER UPDATE ON users
    FOR EACH ROW
    BEGIN
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Create index for subscription queries
CREATE INDEX IF NOT EXISTS idx_users_subscription ON users(subscription_plan, subscription_status);
CREATE INDEX IF NOT EXISTS idx_users_trial_dates ON users(trial_start_date, trial_end_date);
