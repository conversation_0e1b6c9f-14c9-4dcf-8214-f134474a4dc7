-- MANUAL MIGRATION COMMANDS FOR REMOTE DATABASE
-- Execute these commands in the Cloudflare Dashboard D1 Console

-- 1. Add subscription_plan column
ALTER TABLE users ADD COLUMN subscription_plan TEXT DEFAULT 'free';

-- 2. Add subscription_payment_date column  
ALTER TABLE users ADD COLUMN subscription_payment_date DATETIME;

-- 3. Add updated_at column
ALTER TABLE users ADD COLUMN updated_at DATETIME;

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_subscription ON users(subscription_plan, subscription_status);
CREATE INDEX IF NOT EXISTS idx_users_trial_dates ON users(trial_start_date, trial_end_date);

-- 5. Verify the schema (optional)
PRAGMA table_info(users);
