import React, { useState, useEffect } from "react";
import { QRAnalyticsDetail } from "./QRAnalyticsDetail";
import type {
  QRCode,
  QRAnalytics,
  QRAnalyticsResponse,
} from "../../types/dashboard";
import { useAuthStore } from "../../stores/authStore";

interface QRAnalyticsDetailWrapperProps {
  qrCodeId: string;
}

export const QRAnalyticsDetailWrapper: React.FC<
  QRAnalyticsDetailWrapperProps
> = ({ qrCodeId }) => {
  const [qrCode, setQrCode] = useState<QRCode | null>(null);
  const [analytics, setAnalytics] = useState<QRAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { session, status, init, signIn } = useAuthStore();
  // useEffect(() => {
  //   const loadData = async () => {
  //     try {
  //       setLoading(true);
  //       setError(null);

  //       // Fetch analytics data from API
  //       const response = await fetch(`/api/dashboard/qr-analytics/${qrCodeId}`);
  //       const result: QRAnalyticsResponse = await response.json();

  //       if (result.success && result.data) {
  //         setQrCode(result.data.qrCode);
  //         setAnalytics(result.data.analytics);
  //       } else {
  //         setError(result.error || 'Failed to load analytics data');
  //       }
  //     } catch (err) {
  //       setError('Failed to load analytics data');
  //       console.error('Error fetching analytics:', err);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };
  //   setTimeout(() => {
  //     setLoading(false);
  //   }, 1000);

  //   // loadData();
  // }, [qrCodeId]);

  const handleBack = () => {
    window.location.href = "/dashboard";
  };

  
   // Show login UI when user is not authenticated
   if ( status === 'unauthenticated') {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          {/* Login illustration */}
          <div className="w-24 h-24 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-12 h-12 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-3">Welcome to QRAnalytica</h2>
          <p className="text-gray-600 mb-6 leading-relaxed">
            Sign in to view your QR code analytics.
          </p>

          <div className="flex flex-col gap-3 justify-center">
            <button
              onClick={signIn}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-200 transform hover:scale-105 shadow-lg flex items-center justify-center gap-2"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Sign in with Google
            </button>
          </div>
        </div>
      </div>
    );
  }
  // if (loading) {
  //   return (
  //     <div className="flex items-center justify-center min-h-[400px]">
  //       <div className="text-center">
  //         <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
  //         <p className="text-muted-foreground">Loading analytics...</p>
  //       </div>
  //     </div>
  //   );
  // }

  // if (error || !qrCode || !analytics) {
  //   return (
  //     <div className="flex items-center justify-center min-h-[400px]">
  //       <div className="text-center">
  //         <h2 className="text-xl font-semibold mb-2">Error</h2>
  //         <p className="text-muted-foreground mb-4">{error || 'Data not found'}</p>
  //         <button
  //           onClick={handleBack}
  //           className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
  //         >
  //           Back to Dashboard
  //         </button>
  //       </div>
  //     </div>
  //   );
  // }
  if (!qrCodeId) return <div>QR Code not found</div>;
  return (
    <QRAnalyticsDetail
      qrCodeId={qrCodeId}
      // analytics={analytics}
      onBack={handleBack}
    />
  );
};

export default QRAnalyticsDetailWrapper;
