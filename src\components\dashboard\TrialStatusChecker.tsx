import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { UpgradeOverlay } from './UpgradeOverlay';

interface TrialStatusResponse {
  subscription_plan: string;
  subscription_status: string;
  trial_status: {
    hasTrialStarted: boolean;
    isTrialActive: boolean;
    isTrialExpired: boolean;
    trialDaysRemaining: number;
    trialEndDate: string | null;
    canAccessPremiumFeatures: boolean;
  };
  can_access_premium: boolean;
}

export const TrialStatusChecker: React.FC = () => {
  const [showUpgradeOverlay, setShowUpgradeOverlay] = useState(false);
  const [trialStatus, setTrialStatus] = useState<TrialStatusResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const { session, status } = useAuthStore();

  useEffect(() => {
    const checkTrialStatus = async () => {
      if (status !== 'authenticated' || !session) {
        setLoading(false);
        return;
      }

      try {
        // Try to fetch dashboard metrics to check trial status
        const response = await fetch('/api/dashboard/metrics?timezone=UTC', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (response.status === 403) {
          // Trial expired or no premium access
          const result = await response.json();
          if (result.subscription_status) {
            setTrialStatus(result.subscription_status);
            setShowUpgradeOverlay(true);
          }
        } else if (response.ok) {
          // User has access, no need to show overlay
          setShowUpgradeOverlay(false);
        }
      } catch (error) {
        console.error('Error checking trial status:', error);
      } finally {
        setLoading(false);
      }
    };

    checkTrialStatus();
  }, [session, status]);

  if (loading || status !== 'authenticated') {
    return null;
  }

  return (
    <UpgradeOverlay
      isVisible={showUpgradeOverlay}
      title="Upgrade to Professional"
      description={
        trialStatus?.trial_status?.isTrialExpired
          ? "Your 7-day free trial has expired. Upgrade to Professional to continue accessing premium analytics features."
          : "Premium access required. Upgrade to Professional to access advanced analytics features."
      }
      showCloseButton={false}
    />
  );
};

export default TrialStatusChecker;
