import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { UpgradeOverlay } from './UpgradeOverlay';

interface SubscriptionStatusResponse {
  success: boolean;
  data?: {
    subscription_plan: string;
    subscription_status: string;
    trial_status: {
      hasTrialStarted: boolean;
      isTrialActive: boolean;
      isTrialExpired: boolean;
      trialDaysRemaining: number;
      trialEndDate: string | null;
      canAccessPremiumFeatures: boolean;
    };
    can_access_premium: boolean;
  };
  error?: string;
}

export const TrialStatusChecker: React.FC = () => {
  const [showUpgradeOverlay, setShowUpgradeOverlay] = useState(false);
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionStatusResponse['data'] | null>(null);
  const [loading, setLoading] = useState(true);
  const { session, status } = useAuthStore();

  useEffect(() => {
    const checkSubscriptionStatus = async () => {
      if (status !== 'authenticated' || !session) {
        setLoading(false);
        return;
      }

      try {
        // Call the dedicated subscription status API
        const response = await fetch('/api/subscription-status', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (response.ok) {
          const result: SubscriptionStatusResponse = await response.json();

          if (result.success && result.data) {
            setSubscriptionData(result.data);

            // Show upgrade overlay if user doesn't have premium access
            if (!result.data.can_access_premium) {
              setShowUpgradeOverlay(true);
            } else {
              setShowUpgradeOverlay(false);
            }
          }
        } else if (response.status === 401) {
          // User not authenticated
          setShowUpgradeOverlay(false);
        } else {
          console.error('Error checking subscription status:', response.status);
        }
      } catch (error) {
        console.error('Error checking subscription status:', error);
      } finally {
        setLoading(false);
      }
    };

    checkSubscriptionStatus();
  }, [session, status]);

  if (loading || status !== 'authenticated') {
    return null;
  }

  return (
    <UpgradeOverlay
      isVisible={showUpgradeOverlay}
      title="Upgrade to Professional"
      description={
        subscriptionData?.trial_status?.isTrialExpired
          ? "Your 7-day free trial has expired. Upgrade to Professional to continue accessing premium analytics features."
          : "Premium access required. Upgrade to Professional to access advanced analytics features."
      }
      showCloseButton={false}
    />
  );
};

export default TrialStatusChecker;
