---
import '../styles/global.css';
import Sidebar from '../components/Sidebar';
import CrispChat from '../components/CrispChat';
import TrialStatusChecker from '../components/dashboard/TrialStatusChecker';

export interface Props {
  title?: string;
}

const {
  title = 'QR Analytics Dashboard',
} = Astro.props as Props;

---

<!doctype html>
<html lang="en" class="h-full">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="description" content="QRAnalytica administration dashboard" />
    <title>{title}</title>
  </head>
  <body class="h-full flex bg-gradient-to-br from-gray-50 to-gray-100 text-gray-900 antialiased">
    <!-- Sidebar -->
    <aside
      id="sidebar"
      class="fixed inset-y-0 left-0 z-40 w-72 bg-gradient-to-b from-[#2C3E50] to-[#34495E] text-white transform -translate-x-full transition-all duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 shadow-2xl"
    >
      <Sidebar client:load />
    </aside>

    <!-- Overlay for mobile -->
    <div id="sidebar-overlay" class="md:hidden fixed inset-0 bg-black/60 opacity-0 pointer-events-none transition-opacity backdrop-blur-sm"></div>

    <!-- Main content wrapper -->
    <div class="flex-1 flex flex-col min-h-screen">
      <!-- Enhanced Top bar -->
      <header class="bg-white/95 backdrop-blur-sm border-b border-gray-200/60 h-20 flex items-center px-4 sm:px-6 lg:px-8 shadow-sm">
        <div class="flex items-center justify-between w-full">
          <!-- Left section: Mobile toggle + Title + Breadcrumbs -->
          <div class="flex items-center space-x-4 flex-1">
            <!-- Mobile toggle -->
            <button id="sidebarToggle" class="md:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-[#18BC9C] rounded-lg transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5m-16.5 5.25h16.5m-16.5 5.25h16.5" />
              </svg>
            </button>

            <!-- Title and Breadcrumbs -->
            <div class="flex flex-col">
              <h1 class="text-xl font-bold text-gray-900 tracking-tight">{title}</h1>
              <nav class="hidden sm:flex text-sm text-gray-500" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                  <li><a href="/dashboard" class="hover:text-gray-700 transition-colors">Dashboard</a></li>
                  <li class="flex items-center">
                    <svg class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-gray-900 font-medium">Overview</span>
                  </li>
                </ol>
              </nav>
            </div>
          </div>

          <!-- Right section: Search + Notifications + Profile -->
          
        </div>
        <slot name="header" />
      </header>

      <!-- Page content -->
      <main class="flex-1 p-6 sm:p-8 lg:p-12 overflow-y-auto bg-gradient-to-br from-white/50 to-gray-50/50 backdrop-blur-sm">
        <div class="max-w-7xl mx-auto">
          <slot />
        </div>
      </main>
    </div>

    <!-- Trial Status Checker -->
    <TrialStatusChecker client:load />

    <!-- Crisp Chat Integration -->
    <CrispChat client:load />

    <script>
      // Enhanced dashboard interactions
      document.addEventListener('DOMContentLoaded', () => {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        const toggle = document.getElementById('sidebarToggle');
        const userMenuButton = document.getElementById('userMenuButton');
        const userDropdown = document.getElementById('userDropdown');

        // Sidebar functionality
        const openSidebar = () => {
          sidebar?.classList.remove('-translate-x-full');
          overlay?.classList.remove('opacity-0', 'pointer-events-none');
          overlay?.classList.add('opacity-100');
        };

        const closeSidebar = () => {
          sidebar?.classList.add('-translate-x-full');
          overlay?.classList.add('opacity-0');
          overlay?.classList.add('pointer-events-none');
        };

        toggle?.addEventListener('click', () => {
          if (sidebar?.classList.contains('-translate-x-full')) {
            openSidebar();
          } else {
            closeSidebar();
          }
        });

        overlay?.addEventListener('click', closeSidebar);

        // User dropdown functionality
        let isDropdownOpen = false;

        const toggleDropdown = () => {
          isDropdownOpen = !isDropdownOpen;
          if (isDropdownOpen) {
            userDropdown?.classList.remove('hidden');
            userDropdown?.classList.add('animate-in', 'fade-in-0', 'zoom-in-95');
          } else {
            userDropdown?.classList.add('hidden');
            userDropdown?.classList.remove('animate-in', 'fade-in-0', 'zoom-in-95');
          }
        };

        const closeDropdown = () => {
          if (isDropdownOpen) {
            isDropdownOpen = false;
            userDropdown?.classList.add('hidden');
            userDropdown?.classList.remove('animate-in', 'fade-in-0', 'zoom-in-95');
          }
        };

        userMenuButton?.addEventListener('click', (e) => {
          e.stopPropagation();
          toggleDropdown();
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
          const target = e.target as Element;
          if (!userMenuButton?.contains(target) && !userDropdown?.contains(target)) {
            closeDropdown();
          }
        });

        // Close dropdown on escape key
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape') {
            closeDropdown();
          }
        });

        // Search functionality
        const searchInput = document.querySelector('input[placeholder="Search QR codes..."]') as HTMLInputElement;
        searchInput?.addEventListener('keydown', (e) => {
          if (e.key === 'Enter') {
            const query = searchInput.value.trim();
            if (query) {
              // Implement search functionality
              console.log('Searching for:', query);
              // You can redirect to search results or filter current view
            }
          }
        });
      });
    </script>
  </body>
</html> 