import type { D1Database } from '@cloudflare/workers-types';

export interface UserSubscription {
  id: string;
  email: string;
  name: string | null;
  trial_start_date: string | null;
  trial_end_date: string | null;
  subscription_plan: string;
  subscription_status: string;
  subscription_payment_date: string | null;
  created_at: string;
}

export interface TrialStatus {
  hasTrialStarted: boolean;
  isTrialActive: boolean;
  isTrialExpired: boolean;
  trialDaysRemaining: number;
  trialEndDate: Date | null;
  canAccessPremiumFeatures: boolean;
}

/**
 * Get user subscription information from database
 */
export async function getUserSubscription(db: D1Database, userId: string): Promise<UserSubscription | null> {
  try {
    const result = await db
      .prepare(`
        SELECT id, email, name, trial_start_date, trial_end_date, 
               subscription_plan, subscription_status, subscription_payment_date, created_at
        FROM users 
        WHERE id = ?
      `)
      .bind(userId)
      .first();

    return result as UserSubscription | null;
  } catch (error) {
    console.error('Error fetching user subscription:', error);
    return null;
  }
}

/**
 * Check trial status for a user
 */
export function getTrialStatus(user: UserSubscription | null): TrialStatus {
  if (!user) {
    return {
      hasTrialStarted: false,
      isTrialActive: false,
      isTrialExpired: false,
      trialDaysRemaining: 0,
      trialEndDate: null,
      canAccessPremiumFeatures: false
    };
  }

  const now = new Date();
  const trialStartDate = user.trial_start_date ? new Date(user.trial_start_date) : null;
  const trialEndDate = user.trial_end_date ? new Date(user.trial_end_date) : null;

  const hasTrialStarted = !!trialStartDate;
  const isTrialActive = hasTrialStarted && trialEndDate && now <= trialEndDate;
  const isTrialExpired = hasTrialStarted && trialEndDate && now > trialEndDate;

  let trialDaysRemaining = 0;
  if (isTrialActive && trialEndDate) {
    const timeDiff = trialEndDate.getTime() - now.getTime();
    trialDaysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  // User can access premium features if:
  // 1. They have an active trial, OR
  // 2. They have a professional/enterprise subscription that's active
  const canAccessPremiumFeatures = 
    isTrialActive || 
    (user.subscription_plan !== 'free' && user.subscription_status === 'active');

  return {
    hasTrialStarted,
    isTrialActive,
    isTrialExpired,
    trialDaysRemaining,
    trialEndDate,
    canAccessPremiumFeatures
  };
}

/**
 * Start trial for a new user (7 days from now)
 */
export async function startUserTrial(db: D1Database, userId: string): Promise<boolean> {
  try {
    const now = new Date();
    const trialEndDate = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000)); // 7 days from now

    await db
      .prepare(`
        UPDATE users 
        SET trial_start_date = ?, 
            trial_end_date = ?,
            subscription_plan = 'professional',
            subscription_status = 'trial',
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `)
      .bind(now.toISOString(), trialEndDate.toISOString(), userId)
      .run();

    return true;
  } catch (error) {
    console.error('Error starting user trial:', error);
    return false;
  }
}

/**
 * Upgrade user to professional plan
 */
export async function upgradeUserToProfessional(db: D1Database, userId: string): Promise<boolean> {
  try {
    const now = new Date();

    await db
      .prepare(`
        UPDATE users 
        SET subscription_plan = 'professional',
            subscription_status = 'active',
            subscription_payment_date = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `)
      .bind(now.toISOString(), userId)
      .run();

    return true;
  } catch (error) {
    console.error('Error upgrading user to professional:', error);
    return false;
  }
}

/**
 * Check if user has access to premium features
 */
export async function userHasPremiumAccess(db: D1Database, userId: string): Promise<boolean> {
  const user = await getUserSubscription(db, userId);
  const trialStatus = getTrialStatus(user);
  return trialStatus.canAccessPremiumFeatures;
}

/**
 * Get subscription status for API responses
 */
export async function getSubscriptionStatusForAPI(db: D1Database, userId: string) {
  const user = await getUserSubscription(db, userId);
  const trialStatus = getTrialStatus(user);

  return {
    subscription_plan: user?.subscription_plan || 'free',
    subscription_status: user?.subscription_status || 'active',
    trial_status: trialStatus,
    can_access_premium: trialStatus.canAccessPremiumFeatures
  };
}
