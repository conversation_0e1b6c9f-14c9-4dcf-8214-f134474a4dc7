import type { APIRoute } from 'astro';
import {
  getTimezoneFromRequest,
  getTimezoneAwareDateFilter,
  isValidTimezone
} from '../../../lib/timezone-utils';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get URL parameters
    const url = new URL(request.url);
    const dateRange = url.searchParams.get('range') || '7d';
    const qrCodeId = url.searchParams.get('qr_code_id');

    // Get user timezone
    const userTimezone = getTimezoneFromRequest(request);
    console.log('Debug: User timezone for overview:', userTimezone);

    // Validate timezone
    if (!isValidTimezone(userTimezone)) {
      console.warn('Invalid timezone provided:', userTimezone, 'falling back to UTC');
    }

    // Calculate timezone-aware date filter
    const dateFilter = getTimezoneAwareDateFilter(dateRange, userTimezone, 'scan_time');

    // Add QR code filter if specified
    const qrCodeFilter = qrCodeId ? "AND qr_code_id = ?" : "";

    // Get total scans
    let totalScansQuery = `
      SELECT COUNT(*) as count
      FROM qr_code_scan_analytics
      WHERE 1=1 ${dateFilter} ${qrCodeFilter}
    `;
    console.log(`Debug: Total scans query: ${totalScansQuery}`);
    const totalScansResult = qrCodeId ?
      await db.prepare(totalScansQuery).bind(qrCodeId).first() :
      await db.prepare(totalScansQuery).first();
    const totalScans = Number(totalScansResult?.count || 0);
    console.log(`Debug: Total scans result:`, totalScansResult, `Parsed total scans: ${totalScans}`);

    // Get unique users (unique IPs)
    let uniqueUsersQuery = `
      SELECT COUNT(DISTINCT ip) as count
      FROM qr_code_scan_analytics
      WHERE ip IS NOT NULL ${dateFilter} ${qrCodeFilter}
    `;
    const uniqueUsersResult = qrCodeId ?
      await db.prepare(uniqueUsersQuery).bind(qrCodeId).first() :
      await db.prepare(uniqueUsersQuery).first();
    const uniqueUsers = uniqueUsersResult?.count || 0;

    // Get total QR codes (if filtering by QR code, return 1, otherwise all)
    let totalQRCodes;
    if (qrCodeId) {
      totalQRCodes = 1;
    } else {
      const totalQRCodesResult = await db.prepare(`
        SELECT COUNT(*) as count FROM qr_codes
      `).first();
      totalQRCodes = totalQRCodesResult?.count || 0;
    }

    // Calculate scan velocity (scans per hour and per day)
    let hourlyVelocityQuery = `
      SELECT COUNT(*) as count
      FROM qr_code_scan_analytics
      WHERE scan_time >= datetime('now', '-1 hour') ${qrCodeFilter}
    `;
    const hourlyVelocityResult = qrCodeId ?
      await db.prepare(hourlyVelocityQuery).bind(qrCodeId).first() :
      await db.prepare(hourlyVelocityQuery).first();
    const hourlyVelocity = hourlyVelocityResult?.count || 0;

    let dailyVelocityQuery = `
      SELECT COUNT(*) as count
      FROM qr_code_scan_analytics
      WHERE scan_time >= datetime('now', '-1 day') ${qrCodeFilter}
    `;
    const dailyVelocityResult = qrCodeId ?
      await db.prepare(dailyVelocityQuery).bind(qrCodeId).first() :
      await db.prepare(dailyVelocityQuery).first();
    const dailyVelocity = dailyVelocityResult?.count || 0;

    // Get top performing QR codes (or current QR code details if filtering)
    let topPerformersResult;
    if (qrCodeId) {
      // If filtering by QR code, get details of that specific QR code
      topPerformersResult = await db.prepare(`
        SELECT
          qr.id,
          qr.name,
          COUNT(scan.id) as scans
        FROM qr_codes qr
        LEFT JOIN qr_code_scan_analytics scan ON qr.id = scan.qr_code_id
        WHERE qr.id = ? ${dateFilter.replace('scan_time', 'scan.scan_time')}
        GROUP BY qr.id, qr.name
      `).bind(qrCodeId).all();
    } else {
      // Get top performing QR codes
      topPerformersResult = await db.prepare(`
        SELECT
          qr.id,
          qr.name,
          COUNT(scan.id) as scans
        FROM qr_codes qr
        LEFT JOIN qr_code_scan_analytics scan ON qr.id = scan.qr_code_id
        WHERE 1=1 ${dateFilter.replace('scan_time', 'scan.scan_time')}
        GROUP BY qr.id, qr.name
        HAVING scans > 0
        ORDER BY scans DESC
        LIMIT 10
      `).all();
    }

    const topPerformers = (topPerformersResult.results || []).map((qr: any) => ({
      id: qr.id,
      name: qr.name || `QR Code ${qr.id.slice(0, 8)}`,
      scans: qr.scans,
      change: 0 // TODO: Calculate actual change percentage
    }));

    // Calculate conversion rate (placeholder - adjust based on your business logic)
    const conversionRate = (typeof totalQRCodes === 'number' && totalQRCodes > 0 && typeof uniqueUsers === 'number') ? (uniqueUsers / totalQRCodes) * 100 : 0;

    const overview = {
      totalScans,
      uniqueUsers,
      totalQRCodes,
      scanVelocity: {
        hourly: hourlyVelocity,
        daily: dailyVelocity
      },
      conversionRate: Math.round(conversionRate * 100) / 100,
      topPerformers,
      timezone: userTimezone // Include timezone info for frontend reference
    };

    return new Response(JSON.stringify(overview), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Analytics overview API error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to fetch analytics overview' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
