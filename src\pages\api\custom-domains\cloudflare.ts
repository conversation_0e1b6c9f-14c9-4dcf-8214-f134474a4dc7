import type { APIRoute } from 'astro';
import { createCloudflareAPI } from '../../../lib/cloudflare-api';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: "Database not configured." 
      }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if Cloudflare credentials are available
    if (!env.CLOUDFLARE_API_TOKEN || !env.CLOUDFLARE_ACCOUNT_ID) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Cloudflare API credentials not configured'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { domain_id, action, user_id } = body;

    if (!domain_id || !action || !user_id) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Domain ID, action, and user_id are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (!['setup', 'remove'].includes(action)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Action must be either "setup" or "remove"'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get domain details
    const domain = await db.prepare(`
      SELECT 
        id,
        domain,
        pages_project_name,
        cloudflare_zone_id,
        cloudflare_custom_hostname_id,
        status
      FROM custom_domains 
      WHERE id = ? AND user_id = ?
    `).bind(domain_id, user_id).first();

    if (!domain) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Domain not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const cloudflareAPI = createCloudflareAPI(env);
    let updateData: any = {};
    let message = '';

    try {
      if (action === 'setup') {
        // Setup Cloudflare integration
        if (!(domain as any).pages_project_name) {
          return new Response(JSON.stringify({
            success: false,
            error: 'Pages project name is required for Cloudflare setup'
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        // Add domain to Cloudflare Pages project
        const result = await cloudflareAPI.addPagesDomain(
          (domain as any).pages_project_name, 
          (domain as any).domain
        );

        if ((result as any).success) {
          // Get zone information for the domain
          const zone = await cloudflareAPI.getZoneByDomain((domain as any).domain);
          
          updateData = {
            cloudflare_zone_id: zone?.id || null,
            status: 'active',
            error_message: null
          };
          message = 'Domain successfully configured with Cloudflare Pages';
        } else {
          updateData = {
            error_message: `Cloudflare setup failed: ${JSON.stringify((result as any).errors || result)}`
          };
          message = 'Failed to configure domain with Cloudflare Pages';
        }

      } else if (action === 'remove') {
        // Remove Cloudflare integration
        if ((domain as any).pages_project_name) {
          try {
            await cloudflareAPI.removePagesDomain(
              (domain as any).pages_project_name, 
              (domain as any).domain
            );
          } catch (error) {
            console.error('Failed to remove domain from Pages:', error);
            // Continue with database update even if API call fails
          }
        }

        updateData = {
          cloudflare_zone_id: null,
          cloudflare_custom_hostname_id: null,
          status: 'pending',
          error_message: null
        };
        message = 'Domain removed from Cloudflare Pages';
      }

      // Update domain in database
      const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
      const updateValues = Object.values(updateData);
      
      const updateResult = await db.prepare(`
        UPDATE custom_domains 
        SET ${updateFields}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ?
      `).bind(...updateValues, domain_id, user_id).run();

      if (!updateResult.success) {
        return new Response(JSON.stringify({
          success: false,
          error: 'Failed to update domain in database'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      return new Response(JSON.stringify({
        success: action === 'setup' ? (updateData.error_message === null) : true,
        message,
        error: updateData.error_message
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Cloudflare integration error:', error);
      
      // Update domain with error message
      await db.prepare(`
        UPDATE custom_domains 
        SET error_message = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ?
      `).bind(`${action} failed: ${error.message}`, domain_id, user_id).run();

      return new Response(JSON.stringify({
        success: false,
        error: `Failed to ${action} Cloudflare integration: ${error.message}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Error in Cloudflare integration:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to process Cloudflare integration'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const GET: APIRoute = async ({ url, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: "Database not configured." 
      }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const searchParams = url.searchParams;
    const domainId = searchParams.get("domain_id");
    const userId = searchParams.get("user_id");

    if (!domainId || !userId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Domain ID and user_id are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get domain Cloudflare status
    const domain = await db.prepare(`
      SELECT 
        id,
        domain,
        cloudflare_zone_id,
        cloudflare_custom_hostname_id,
        pages_project_name,
        status,
        error_message
      FROM custom_domains 
      WHERE id = ? AND user_id = ?
    `).bind(domainId, userId).first();

    if (!domain) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Domain not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const cloudflareStatus = {
      zone_exists: !!(domain as any).cloudflare_zone_id,
      custom_hostname_id: (domain as any).cloudflare_custom_hostname_id,
      pages_project: (domain as any).pages_project_name,
      integrated: !!(domain as any).cloudflare_zone_id,
      dns_records: []
    };

    // If Cloudflare credentials are available, get additional status
    if (env.CLOUDFLARE_API_TOKEN && env.CLOUDFLARE_ACCOUNT_ID && (domain as any).cloudflare_zone_id) {
      try {
        const cloudflareAPI = createCloudflareAPI(env);
        const dnsRecords = await cloudflareAPI.listDNSRecords((domain as any).cloudflare_zone_id);
        cloudflareStatus.dns_records = (dnsRecords as any).result || [];
      } catch (error) {
        console.error('Failed to fetch DNS records:', error);
      }
    }

    return new Response(JSON.stringify({
      success: true,
      domain: {
        ...domain
      },
      cloudflare_status: cloudflareStatus
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error checking Cloudflare status:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to check Cloudflare status'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
