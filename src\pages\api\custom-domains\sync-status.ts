import type { APIRoute } from 'astro';
import { createCloudflareAPI } from '../../../lib/cloudflare-api';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: "Database not configured." 
      }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if Cloudflare credentials are available
    if (!env.CLOUDFLARE_API_TOKEN || !env.CLOUDFLARE_ACCOUNT_ID) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Cloudflare API credentials not configured'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { domain_id, user_id } = body as { domain_id?: string; user_id: string };

    if (!user_id) {
      return new Response(JSON.stringify({
        success: false,
        error: 'user_id is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const cloudflareAPI = createCloudflareAPI(env);
    let syncResults: any[] = [];

    try {
      if (domain_id) {
        // Sync specific domain
        const domain = await db.prepare(`
          SELECT id, domain, pages_project_name, status
          FROM custom_domains 
          WHERE id = ? AND user_id = ?
        `).bind(domain_id, user_id).first();

        if (!domain) {
          return new Response(JSON.stringify({
            success: false,
            error: 'Domain not found'
          }), {
            status: 404,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        const result = await syncDomainStatus(cloudflareAPI, db, domain as any);
        syncResults.push(result);

      } else {
        // Sync all domains for user
        const domains = await db.prepare(`
          SELECT id, domain, pages_project_name, status
          FROM custom_domains 
          WHERE user_id = ? AND pages_project_name IS NOT NULL
        `).bind(user_id).all();

        for (const domain of domains.results) {
          const result = await syncDomainStatus(cloudflareAPI, db, domain as any);
          syncResults.push(result);
        }
      }

      const successCount = syncResults.filter(r => r.success).length;
      const totalCount = syncResults.length;

      return new Response(JSON.stringify({
        success: true,
        message: `Synced ${successCount}/${totalCount} domains successfully`,
        results: syncResults
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('Domain sync error:', error);
      return new Response(JSON.stringify({
        success: false,
        error: `Failed to sync domain status: ${(error as Error).message}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Error in domain sync:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to sync domain status'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

async function syncDomainStatus(cloudflareAPI: any, db: any, domain: any) {
  try {
    if (!domain.pages_project_name) {
      return {
        domain: domain.domain,
        success: false,
        error: 'No Pages project name configured'
      };
    }

    // Get domain status from Cloudflare Pages
    const cfStatus = await cloudflareAPI.getPagesDomainStatus(
      domain.pages_project_name, 
      domain.domain
    );

    if ((cfStatus as any).success && (cfStatus as any).result) {
      const result = (cfStatus as any).result;
      
      // Map Cloudflare status to our status
      let newStatus = 'pending';
      let sslStatus = 'pending';
      let verified = 0;
      let errorMessage = null;

      if (result.status === 'active' && result.verification_data?.status === 'active') {
        newStatus = 'active';
        verified = 1;
        
        if (result.validation_data?.status === 'active') {
          sslStatus = 'active';
        }
      } else if (result.status === 'pending') {
        newStatus = 'pending';
      } else {
        newStatus = 'failed';
        errorMessage = `Cloudflare status: ${result.status}`;
      }

      // Update domain in database
      const updateResult = await db.prepare(`
        UPDATE custom_domains 
        SET 
          status = ?,
          ssl_status = ?,
          verified = ?,
          error_message = ?,
          last_verified_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(
        newStatus,
        sslStatus,
        verified,
        errorMessage,
        domain.id
      ).run();

      if (updateResult.success) {
        return {
          domain: domain.domain,
          success: true,
          old_status: domain.status,
          new_status: newStatus,
          ssl_status: sslStatus,
          verified: verified === 1,
          // cloudflare_data: result
        };
      } else {
        return {
          domain: domain.domain,
          success: false,
          error: 'Failed to update database'
        };
      }

    } else {
      return {
        domain: domain.domain,
        success: false,
        error: 'Domain not found in Cloudflare Pages or API error',
        cloudflare_response: cfStatus
      };
    }

  } catch (error) {
    console.error(`Error syncing domain ${domain.domain}:`, error);
    return {
      domain: domain.domain,
      success: false,
      error: (error as Error).message
    };
  }
}

export const GET: APIRoute = async ({ url, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: "Database not configured." 
      }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const searchParams = url.searchParams;
    const userId = searchParams.get("user_id");

    if (!userId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'user_id is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get domains that need syncing (have Pages project but might be outdated)
    const domains = await db.prepare(`
      SELECT 
        id,
        domain,
        pages_project_name,
        status,
        verified,
        last_verified_at,
        updated_at
      FROM custom_domains 
      WHERE user_id = ? AND pages_project_name IS NOT NULL
      ORDER BY updated_at DESC
    `).bind(userId).all();

    return new Response(JSON.stringify({
      success: true,
      domains: domains.results.map((domain: any) => ({
        ...domain,
        verified: domain.verified === 1,
        needs_sync: !domain.last_verified_at || 
                   (new Date().getTime() - new Date(domain.updated_at).getTime()) > 300000 // 5 minutes
      }))
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error getting sync status:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to get sync status'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
