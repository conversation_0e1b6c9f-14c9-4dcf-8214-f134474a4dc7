import type { APIRoute } from "astro";
import { createCloudflareAPI } from "../../../lib/cloudflare-api";

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Database not configured.",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    const body = await request.json();
    const { domain_id, user_id } = body;

    if (!domain_id || !user_id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Domain ID and user_id are required",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Get domain details
    const domain = await db
      .prepare(
        `
      SELECT 
        id,
        domain,
        verification_token,
        pages_project_name,
        status
      FROM custom_domains 
      WHERE id = ? AND user_id = ?
    `
      )
      .bind(domain_id, user_id)
      .first();

    if (!domain) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Domain not found",
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    let verificationSuccess = false;
    let errorMessage = null;

    try {
      // Try to verify domain using Cloudflare API if available
      if (env.CLOUDFLARE_API_TOKEN && env.CLOUDFLARE_ACCOUNT_ID) {
        const cloudflareAPI = createCloudflareAPI(env);

        // use getPagesDomainStatus to check if the domain is verified
        const domainStatus = await cloudflareAPI.getPagesDomainStatus(
          "qr-redirect-backend-v2",
          domain.domain
        );
        console.log("🚀 ~ POST ~ domainStatus:", domainStatus);
        if (domainStatus.success) {
          verificationSuccess = true;
        } else {
          verificationSuccess = false;
        }
      } else {
        // Fallback: mark as verified if no Cloudflare credentials
        verificationSuccess = true;
        errorMessage =
          "Domain marked as verified (Cloudflare API not configured)";
      }
    } catch (error) {
      console.error("Domain verification error:", error);
      errorMessage = `Verification failed: ${error.message}`;
    }

    // Update domain status in database
    const updateResult = await db
      .prepare(
        `
      UPDATE custom_domains 
      SET 
        verified = ?,
        status = ?,
        last_verified_at = CURRENT_TIMESTAMP,
        error_message = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND user_id = ?
    `
      )
      .bind(
        verificationSuccess ? 1 : 0,
        verificationSuccess ? "active" : "failed",
        errorMessage,
        domain_id,
        user_id
      )
      .run();

    if (!updateResult.success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to update domain status",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: verificationSuccess,
        message: verificationSuccess
          ? "Domain verified successfully"
          : "Domain verification failed",
        error: errorMessage,
        verified: verificationSuccess,
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Error verifying domain:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Failed to verify domain",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
};

export const GET: APIRoute = async ({ url, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Database not configured.",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    const searchParams = url.searchParams;
    const domainId = searchParams.get("domain_id");
    const userId = searchParams.get("user_id");

    if (!domainId || !userId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Domain ID and user_id are required",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Get domain verification status
    const domain = await db
      .prepare(
        `
      SELECT 
        id,
        domain,
        verified,
        status,
        last_verified_at,
        error_message
      FROM custom_domains 
      WHERE id = ? AND user_id = ?
    `
      )
      .bind(domainId, userId)
      .first();

    if (!domain) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Domain not found",
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        domain: {
          ...domain,
          verified: (domain as any).verified === 1,
        },
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Error checking verification status:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Failed to check verification status",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
};
