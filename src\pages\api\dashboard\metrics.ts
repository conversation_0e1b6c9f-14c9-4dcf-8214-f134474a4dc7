import type { APIRoute } from 'astro';
import { getDashboardMetrics, getDatabase } from '../../../lib/database';
import { getUserIdFromRequest, isAuthenticated } from '../../../lib/auth-utils';
import { userHasPremiumAccess, getSubscriptionStatusForAPI } from '../../../lib/subscription-utils';
import type { DashboardMetricsResponse } from '../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Get user ID from session (if available)
    const userId = getUserIdFromRequest(request);
    console.log('Debug: Dashboard metrics - User ID:', userId);

    // Check authentication
    const isUserAuthenticated = isAuthenticated(request);
    console.log('Debug: User authenticated:', isUserAuthenticated);

    if (!userId || !isUserAuthenticated) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User not authenticated'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      });
    }

    // Check if user has premium access (trial or paid)
    const hasPremiumAccess = await userHasPremiumAccess(db, userId);
    if (!hasPremiumAccess) {
      const subscriptionStatus = await getSubscriptionStatusForAPI(db, userId);
      return new Response(JSON.stringify({
        success: false,
        error: 'Premium access required',
        subscription_status: subscriptionStatus
      }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      });
    }

    // Get timezone from query parameters
    const url = new URL(request.url);
    const userTimezone = url.searchParams.get('timezone') || 'UTC';
    console.log('Debug: User timezone:', userTimezone);

    // Fetch dashboard metrics (user-specific if authenticated, otherwise all data)
    const metrics = await getDashboardMetrics(db, userId || undefined, userTimezone);
    
    const response: DashboardMetricsResponse = {
      success: true,
      data: metrics
    };
    
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  } catch (error) {
    console.error('Dashboard metrics API error:', error);
    
    const response: DashboardMetricsResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch dashboard metrics'
    };
    
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
