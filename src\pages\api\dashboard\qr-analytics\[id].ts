import type { APIRoute } from 'astro';
import { getQRCodeAnalytics, getDatabase } from '../../../../lib/database';
import { getUserIdFromRequest, isAuthenticated } from '../../../../lib/auth-utils';
import type { QRAnalyticsResponse } from '../../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ params, request, locals }) => {
  try {
    // Get QR code ID from params
    const qrCodeId = params.id;

    if (!qrCodeId) {
      const response: QRAnalyticsResponse = {
        success: false,
        error: 'QR code ID is required'
      };

      return new Response(JSON.stringify(response), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Get user ID from session (if available)
    const userId = getUserIdFromRequest(request);
    console.log('Debug: QR analytics - User ID:', userId, 'QR Code ID:', qrCodeId);

    // Check authentication
    const isUserAuthenticated = isAuthenticated(request);
    console.log('Debug: User authenticated:', isUserAuthenticated);

    if (!userId || !isUserAuthenticated) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User not authenticated'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      });
    }

    // Fetch QR code analytics (user-specific if authenticated, otherwise all data)
    const result = await getQRCodeAnalytics(db, qrCodeId, userId);
    
    const response: QRAnalyticsResponse = {
      success: true,
      data: result
    };
    
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  } catch (error) {
    console.error('QR analytics API error:', error);
    
    const response: QRAnalyticsResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch QR code analytics'
    };
    
    const statusCode = error instanceof Error && error.message === 'QR code not found' ? 404 : 500;
    
    return new Response(JSON.stringify(response), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
