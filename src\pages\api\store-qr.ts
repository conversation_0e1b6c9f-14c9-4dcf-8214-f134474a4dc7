import type { APIRoute } from "astro";
import { v4 as uuidv4 } from "uuid";
export const prerender = false;

// Helper function to extract content-specific data
const extractContentData = (contentType: string, formData: any, qrData: string) => {
  let contentData: any = {};
  let originalUrl: string | null = null;
  let emailAddress: string | null = null;
  let wifiSsid: string | null = null;

  switch (contentType) {
    case 'url':
      originalUrl = formData?.url || qrData;
      contentData = { url: originalUrl };
      break;
    
    case 'whatsapp':
      const { countryCode, phoneNumber, message } = formData?.whatsapp || {};
      contentData = { countryCode, phoneNumber, message };
      originalUrl = `https://wa.me/${countryCode}${phoneNumber}?text=${message}`;
      break;
    
    case 'email':
      const { email, subject, body } = formData?.email || {};
      emailAddress = email;
      contentData = { email, subject, body };
      break;
    
    case 'wifi':
      const { ssid, password, security, hidden } = formData?.wifi || {};
      wifiSsid = ssid;
      contentData = { ssid, password, security, hidden };
      break;
    
    default:
      contentData = formData || {};
  }

  return { contentData, originalUrl, emailAddress, wifiSsid };
};

export const POST: APIRoute = async ({ request, locals }) => {
  // @ts-ignore
  const env = locals.runtime.env;
  // @ts-ignore
  const db = env.DB as D1Database;

  if (!db) {
    return new Response(JSON.stringify({ error: "D1 database not configured." }), { status: 500 });
  }

  const payload: any = await request.json();
  const qrId = uuidv4();
  const name = payload.name ?? "qr-code";
  const qrOptions = payload.options ?? {}; // QR styling options
  const qrData = qrOptions.data || ""; // The actual QR data string
  const userId = payload.user_id || null; // Optional user ID
  const isDynamic = payload.isDynamic ? 1 : 0; // Convert boolean to integer
  const trackingDomain = payload.domain || payload.tracking_domain || null;
  const redirectUrl = payload.redirect_url || null;
  const userEmail = payload.userEmail || null;
  
  // Extract content type and form data
  const contentType = payload.contentType || 'url';
  const formData = payload.formData || {};
  
  // Generate custom slug based on name (sanitized)
  const customSlug = payload.customUrl || payload.custom_slug || name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special chars
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();

  // Extract content-specific data
  const { contentData, originalUrl, emailAddress, wifiSsid } = extractContentData(contentType, formData, qrData);

  try {
    // First, try to get or create user if email is provided
    let finalUserId = userId;
    if (userEmail && !userId) {
      try {
        // Check if user exists
        const existingUser = await db.prepare(
          `SELECT id FROM users WHERE email = ?`
        ).bind(userEmail).first();

        if (existingUser) {
          finalUserId = existingUser.id;
        } else {
          // Create new user
          const newUserId = uuidv4();
          await db.prepare(
            `INSERT INTO users (id, email) VALUES (?, ?)`
          ).bind(newUserId, userEmail).run();
          finalUserId = newUserId;
        }
      } catch (userErr) {
        console.warn("Failed to handle user:", userErr);
        // Continue without user association
      }
    }

    // Insert QR code with new schema
    await db.prepare(
      `INSERT INTO qr_codes (
        id, user_id, name, data, dynamic, tracking_domain, custom_slug, redirect_url,
        content_type, content_data, original_url, email_address, wifi_ssid
      ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13)`
    ).bind(
      qrId, 
      finalUserId, 
      name, 
      JSON.stringify(qrOptions), // Store QR styling options as JSON
      isDynamic, 
      trackingDomain, 
      customSlug, 
      redirectUrl,
      contentType,
      JSON.stringify(contentData),
      originalUrl,
      emailAddress,
      wifiSsid
    ).run();

    return new Response(JSON.stringify({ 
      id: qrId,
      qr_id: qrId, // Keep for backward compatibility
      custom_slug: customSlug,
      content_type: contentType,
      success: true
    }), {
      headers: { "Content-Type": "application/json" },
      status: 201,
    });
  } catch (err) {
    console.error("Database error:", err);
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    return new Response(JSON.stringify({ error: "Failed to store QR", details: errorMessage }), { status: 500 });
  }
}; 