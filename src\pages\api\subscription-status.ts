import type { APIRoute } from 'astro';
import { getDatabase } from '../../lib/database';
import { getUserIdFromRequest, isAuthenticated } from '../../lib/auth-utils';
import { getUserSubscription, getTrialStatus } from '../../lib/subscription-utils';

export const prerender = false;

interface SubscriptionStatusResponse {
  success: boolean;
  data?: {
    subscription_plan: string;
    subscription_status: string;
    trial_status: {
      hasTrialStarted: boolean;
      isTrialActive: boolean;
      isTrialExpired: boolean;
      trialDaysRemaining: number;
      trialEndDate: string | null;
      canAccessPremiumFeatures: boolean;
    };
    can_access_premium: boolean;
  };
  error?: string;
}

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Get user ID from session
    const userId = getUserIdFromRequest(request);
    console.log('Debug: Subscription status - User ID:', userId);

    // Check authentication
    const isUserAuthenticated = isAuthenticated(request);
    console.log('Debug: User authenticated:', isUserAuthenticated);

    if (!userId || !isUserAuthenticated) {
      const response: SubscriptionStatusResponse = {
        success: false,
        error: 'User not authenticated'
      };

      return new Response(JSON.stringify(response), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      });
    }

    // Get user subscription information
    const userSubscription = await getUserSubscription(db, userId);
    
    if (!userSubscription) {
      const response: SubscriptionStatusResponse = {
        success: false,
        error: 'User subscription not found'
      };

      return new Response(JSON.stringify(response), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      });
    }

    // Get trial status
    const trialStatus = getTrialStatus(userSubscription);

    const response: SubscriptionStatusResponse = {
      success: true,
      data: {
        subscription_plan: userSubscription.subscription_plan,
        subscription_status: userSubscription.subscription_status,
        trial_status: {
          hasTrialStarted: trialStatus.hasTrialStarted,
          isTrialActive: trialStatus.isTrialActive,
          isTrialExpired: trialStatus.isTrialExpired,
          trialDaysRemaining: trialStatus.trialDaysRemaining,
          trialEndDate: trialStatus.trialEndDate?.toISOString() || null,
          canAccessPremiumFeatures: trialStatus.canAccessPremiumFeatures
        },
        can_access_premium: trialStatus.canAccessPremiumFeatures
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Error checking subscription status:', error);
    
    const response: SubscriptionStatusResponse = {
      success: false,
      error: 'Internal server error'
    };

    return new Response(JSON.stringify(response), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  }
};
